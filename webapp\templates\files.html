{% extends "base.html" %}

{% block title %}文件管理 - yt-dlp Web{% endblock %}

{% block extra_css %}
    <!-- Plyr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/plyr/3.7.8/plyr.min.css" />

    <style>
        .file-item {
            transition: all 0.3s ease;
        }
        .file-item:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .file-size {
            color: #6c757d;
            font-size: 0.9em;
        }
        .file-date {
            color: #6c757d;
            font-size: 0.85em;
        }
        .download-btn {
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            transform: scale(1.05);
        }
        .play-btn {
            transition: all 0.3s ease;
        }
        .play-btn:hover {
            transform: scale(1.05);
        }
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* Plyr视频播放器样式 */
        .video-player-modal .modal-dialog {
            max-width: 95vw;
            max-height: 95vh;
        }

        .video-player-modal .modal-content {
            background: #000;
            border: none;
            border-radius: 8px;
            overflow: hidden;
        }

        .video-player-modal .modal-body {
            padding: 0;
        }

        .plyr {
            width: 100%;
            height: 75vh;
        }

        .plyr--video {
            background: #000;
        }

        .plyr__video-wrapper {
            background: #000;
        }

        /* Plyr控件样式自定义 */
        .plyr--full-ui input[type=range] {
            color: #00b3ff;
        }

        .plyr__control--overlaid {
            background: rgba(0, 179, 255, 0.8);
        }

        .plyr__control--overlaid:hover {
            background: rgba(0, 179, 255, 1);
        }

        .plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::before {
            background: #00b3ff;
        }
    </style>
{% endblock %}

{% block content %}

    <div class="container mt-4">
        <!-- 页面标题和统计 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="bi bi-folder2-open"></i> 文件管理</h2>
                <p class="text-muted">管理您下载的所有文件</p>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4 id="totalFiles">0</h4>
                        <small>总文件数</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row mb-3">
            <div class="col">
                <button class="btn btn-primary" onclick="refreshFiles()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新列表
                </button>
                <button class="btn btn-outline-secondary" onclick="clearCompleted()">
                    <i class="bi bi-trash"></i> 清理已完成
                </button>
            </div>
        </div>

        <!-- 文件列表 -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> 下载文件列表</h5>
                    </div>
                    <div class="card-body">
                        <div id="loadingSpinner" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载文件列表...</p>
                        </div>
                        
                        <div id="filesList" style="display: none;">
                            <!-- 文件列表将在这里动态生成 -->
                        </div>
                        
                        <div id="emptyState" class="empty-state" style="display: none;">
                            <i class="bi bi-folder-x" style="font-size: 3rem; color: #dee2e6;"></i>
                            <h4 class="mt-3">暂无文件</h4>
                            <p>您还没有下载任何文件。<a href="/">立即开始下载</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plyr视频播放器模态框 -->
    <div class="modal fade video-player-modal" id="videoPlayerModal" tabindex="-1" aria-labelledby="videoPlayerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-dark text-white">
                    <h5 class="modal-title" id="videoPlayerModalLabel">视频播放器</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <video
                        id="videoPlayer"
                        playsinline
                        controls
                        data-poster="">
                        <source src="" type="video/mp4" />
                        <!-- 备用字幕轨道 -->
                        <track kind="captions" label="中文" srclang="zh" src="" default />
                        <!-- 不支持的浏览器提示 -->
                        <p>
                            您的浏览器不支持HTML5视频播放。
                            <a href="" download>点击下载视频文件</a>
                        </p>
                    </video>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <!-- Plyr JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/plyr/3.7.8/plyr.min.js"></script>

    <script>
        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🎯 文件管理页面加载');

            // 使用Flask-Login认证状态
            if (typeof window.ensureAuthSync === 'function') {
                console.log('🔄 检查认证状态');
                const isAuthenticated = await window.ensureAuthSync();
                if (isAuthenticated) {
                    console.log('✅ 已认证，加载文件');
                    loadFiles();
                } else {
                    console.log('❌ 未认证，显示登录提示');
                    showLoginPrompt();
                }
            } else {
                // 检查基本认证状态
                if (isAuthenticated && currentUser) {
                    loadFiles();
                } else {
                    showLoginPrompt();
                }
            }
        });

        // 旧的认证检查函数已删除，使用Flask-Login

        // 显示登录提示
        function showLoginPrompt() {
            // 隐藏加载状态和空状态
            hideLoading();
            document.getElementById('emptyState').style.display = 'none';

            // 在文件列表区域显示登录提示
            const filesList = document.getElementById('filesList');
            filesList.style.display = 'block';
            filesList.innerHTML = `
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-lock" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <h4>需要登录访问</h4>
                    <p class="text-muted mb-4">请先登录以查看和管理您的文件</p>
                    <button class="btn btn-primary" onclick="goToLogin()">
                        <i class="bi bi-box-arrow-in-right"></i> 立即登录
                    </button>
                </div>
            `;
        }

        // 跳转到登录页
        function goToLogin() {
            window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
        }

        // 跳转到登录页（兼容旧函数名）
        function redirectToLogin() {
            goToLogin();
        }

        // 加载文件列表 - 使用Flask-Login
        async function loadFiles() {
            try {
                showLoading();
                const response = await fetch('/api/files', {
                    credentials: 'same-origin'  // 使用cookies认证
                });
                const data = await response.json();
                
                if (data.success) {
                    displayFiles(data.files);
                    updateStats(data.total);
                } else {
                    showError('获取文件列表失败: ' + (data.error || '未知错误'));
                }
            } catch (error) {
                console.error('加载文件列表失败:', error);
                showError('网络错误，请检查连接');
            }
        }

        // 显示文件列表
        function displayFiles(files) {
            const filesList = document.getElementById('filesList');
            const emptyState = document.getElementById('emptyState');
            
            hideLoading();
            
            if (!files || files.length === 0) {
                filesList.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            emptyState.style.display = 'none';
            filesList.style.display = 'block';
            
            let html = '';
            files.forEach(file => {
                // 检查是否为视频文件
                const isVideo = isVideoFile(file.filename || '');

                html += `
                    <div class="file-item border rounded p-3 mb-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1">
                                    <i class="bi bi-${isVideo ? 'play-circle' : 'file-earmark-arrow-down'} text-primary"></i>
                                    ${escapeHtml(file.filename || '未知文件')}
                                    ${isVideo ? '<span class="badge bg-info ms-2">视频</span>' : ''}
                                </h6>
                                <small class="text-muted">
                                    <i class="bi bi-link-45deg"></i>
                                    ${escapeHtml(file.original_url || '')}
                                </small>
                            </div>
                            <div class="col-md-2 text-center">
                                <span class="file-size">${file.file_size_formatted || '未知'}</span>
                            </div>
                            <div class="col-md-2 text-center">
                                <span class="file-date">${file.created_at_formatted || '未知'}</span>
                            </div>
                            <div class="col-md-2 text-end">
                                ${isVideo ? `
                                    <button class="btn btn-primary btn-sm play-btn me-1"
                                            onclick="playVideo('${escapeHtml(file.filename)}', '${file.download_url}')">
                                        <i class="bi bi-play-fill"></i> 播放
                                    </button>
                                ` : ''}
                                <a href="${file.download_url}"
                                   class="btn btn-success btn-sm download-btn me-1"
                                   download="${escapeHtml(file.filename || 'download')}">
                                    <i class="bi bi-download"></i> 下载
                                </a>
                                <button class="btn btn-outline-danger btn-sm"
                                        onclick="deleteFile('${escapeHtml(file.filename)}', '${file.file_path || ''}')">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            filesList.innerHTML = html;
        }

        // 更新统计信息
        function updateStats(total) {
            document.getElementById('totalFiles').textContent = total || 0;
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('filesList').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        // 刷新文件列表
        function refreshFiles() {
            loadFiles();
        }

        // 删除单个文件
        async function deleteFile(filename, filePath) {
            if (!confirm(`确定要删除文件 "${filename}" 吗？\n\n此操作不可恢复！`)) {
                return;
            }

            try {
                const response = await fetch('/api/files/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'same-origin',  // 使用cookies认证
                    body: JSON.stringify({
                        filename: filename,
                        file_path: filePath
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(`文件 "${filename}" 已删除`);
                    // 刷新文件列表
                    loadFiles();
                } else {
                    showErrorMessage('删除失败: ' + (data.error || '未知错误'));
                }
            } catch (error) {
                console.error('删除文件失败:', error);
                showErrorMessage('删除失败: 网络错误');
            }
        }

        // 清理已完成的下载（占位功能）
        function clearCompleted() {
            if (confirm('确定要清理所有已完成的下载记录吗？')) {
                // TODO: 实现清理功能
                alert('清理功能待实现');
            }
        }

        // 显示错误信息
        function showError(message) {
            hideLoading();
            const filesList = document.getElementById('filesList');
            filesList.style.display = 'block';
            filesList.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    ${escapeHtml(message)}
                </div>
            `;
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            showMessage(message, 'success');
        }

        // 显示错误消息
        function showErrorMessage(message) {
            showMessage(message, 'danger');
        }

        // 显示消息
        function showMessage(message, type) {
            // 创建消息元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // 插入到页面顶部
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);

            // 5秒后自动隐藏
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 检查是否为视频文件
        function isVideoFile(filename) {
            const videoExtensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ogv'];
            const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
            return videoExtensions.includes(extension);
        }

        // Plyr播放器实例
        let currentPlayer = null;

        function playVideo(filename, videoUrl) {
            try {
                console.log('🎬 播放视频:', filename, 'URL:', videoUrl);

                // 设置模态框标题
                document.getElementById('videoPlayerModalLabel').textContent = filename;

                // 如果已有播放器实例，先销毁
                if (currentPlayer) {
                    currentPlayer.destroy();
                    currentPlayer = null;
                }

                // 获取视频元素
                const videoElement = document.getElementById('videoPlayer');

                // 设置视频源
                const sourceElement = videoElement.querySelector('source');
                sourceElement.src = videoUrl;
                sourceElement.type = getVideoMimeType(filename);

                // 设置备用下载链接
                const downloadLink = videoElement.querySelector('a[download]');
                downloadLink.href = videoUrl;
                downloadLink.download = filename;

                // 重新加载视频元素
                videoElement.load();

                // 创建Plyr播放器实例
                currentPlayer = new Plyr('#videoPlayer', {
                    controls: [
                        'play-large', // 大播放按钮
                        'restart', // 重新开始
                        'rewind', // 快退
                        'play', // 播放/暂停
                        'fast-forward', // 快进
                        'progress', // 进度条
                        'current-time', // 当前时间
                        'duration', // 总时长
                        'mute', // 静音
                        'volume', // 音量
                        'captions', // 字幕
                        'settings', // 设置
                        'pip', // 画中画
                        'airplay', // AirPlay
                        'download', // 下载
                        'fullscreen' // 全屏
                    ],
                    settings: ['captions', 'quality', 'speed'],
                    speed: { selected: 1, options: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2] },
                    quality: { default: 720, options: [4320, 2880, 2160, 1440, 1080, 720, 576, 480, 360, 240] },
                    i18n: {
                        restart: '重新播放',
                        rewind: '快退 {seektime}s',
                        play: '播放',
                        pause: '暂停',
                        fastForward: '快进 {seektime}s',
                        seek: '跳转',
                        seekLabel: '{currentTime} / {duration}',
                        played: '已播放',
                        buffered: '已缓冲',
                        currentTime: '当前时间',
                        duration: '总时长',
                        volume: '音量',
                        mute: '静音',
                        unmute: '取消静音',
                        enableCaptions: '启用字幕',
                        disableCaptions: '禁用字幕',
                        download: '下载',
                        enterFullscreen: '进入全屏',
                        exitFullscreen: '退出全屏',
                        frameTitle: '播放器',
                        captions: '字幕',
                        settings: '设置',
                        pip: '画中画',
                        menuBack: '返回上级菜单',
                        speed: '播放速度',
                        normal: '正常',
                        quality: '画质',
                        loop: '循环播放',
                        start: '开始',
                        end: '结束',
                        all: '全部',
                        reset: '重置',
                        disabled: '禁用',
                        enabled: '启用',
                        advertisement: '广告',
                        qualityBadge: {
                            2160: '4K',
                            1440: 'HD',
                            1080: 'HD',
                            720: 'HD',
                            576: 'SD',
                            480: 'SD'
                        }
                    }
                });

                // 监听播放器事件
                currentPlayer.on('ready', () => {
                    console.log('✅ Plyr播放器已准备就绪');
                });

                currentPlayer.on('error', (event) => {
                    console.error('❌ Plyr播放器错误:', event);
                    showErrorMessage('视频播放失败，请检查文件格式或网络连接');
                });

                currentPlayer.on('loadstart', () => {
                    console.log('🔄 开始加载视频');
                });

                currentPlayer.on('canplay', () => {
                    console.log('▶️ 视频可以播放');
                });

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('videoPlayerModal'));
                modal.show();

                // 模态框关闭时暂停视频并清理
                document.getElementById('videoPlayerModal').addEventListener('hidden.bs.modal', function () {
                    if (currentPlayer) {
                        currentPlayer.pause();
                        console.log('⏸️ 视频已暂停');
                    }
                }, { once: true }); // 只监听一次

            } catch (error) {
                console.error('❌ 播放视频失败:', error);
                showErrorMessage('视频播放失败: ' + error.message);
            }
        }

        // 获取视频MIME类型
        function getVideoMimeType(filename) {
            const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
            const mimeTypes = {
                '.mp4': 'video/mp4',
                '.webm': 'video/webm',
                '.ogv': 'video/ogg',
                '.avi': 'video/x-msvideo',
                '.mov': 'video/quicktime',
                '.wmv': 'video/x-ms-wmv',
                '.flv': 'video/x-flv',
                '.mkv': 'video/x-matroska',
                '.m4v': 'video/mp4',
                '.3gp': 'video/3gpp'
            };
            return mimeTypes[extension] || 'video/mp4';
        }

        // 页面卸载时清理Plyr播放器
        window.addEventListener('beforeunload', function() {
            if (currentPlayer) {
                currentPlayer.destroy();
                console.log('🧹 Plyr播放器已清理');
            }
        });
    </script>
{% endblock %}
