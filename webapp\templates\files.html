{% extends "base.html" %}

{% block title %}文件管理 - yt-dlp Web{% endblock %}

{% block extra_css %}
    <style>
        .file-item {
            transition: all 0.3s ease;
        }
        .file-item:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .file-size {
            color: #6c757d;
            font-size: 0.9em;
        }
        .file-date {
            color: #6c757d;
            font-size: 0.85em;
        }
        .download-btn {
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            transform: scale(1.05);
        }
        .play-btn {
            transition: all 0.3s ease;
        }
        .play-btn:hover {
            transform: scale(1.05);
        }
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

    </style>
{% endblock %}

{% block content %}
    <div class="container mt-4">
        <!-- 页面标题和统计 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="bi bi-folder2-open"></i> 文件管理</h2>
                <p class="text-muted">管理您下载的所有文件</p>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4 id="totalFiles">0</h4>
                        <small>总文件数</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row mb-3">
            <div class="col">
                <button class="btn btn-primary" onclick="refreshFiles()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新列表
                </button>
                <button class="btn btn-outline-secondary" onclick="clearCompleted()">
                    <i class="bi bi-trash"></i> 清理已完成
                </button>

            </div>
        </div>

        <!-- 文件列表 -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> 下载文件列表</h5>
                    </div>
                    <div class="card-body">
                        <div id="loadingSpinner" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载文件列表...</p>
                        </div>
                        
                        <div id="filesList" style="display: none;">
                            <!-- 文件列表将在这里动态生成 -->
                        </div>
                        
                        <div id="emptyState" class="empty-state" style="display: none;">
                            <i class="bi bi-folder-x" style="font-size: 3rem; color: #dee2e6;"></i>
                            <h4 class="mt-3">暂无文件</h4>
                            <p>您还没有下载任何文件。<a href="/">立即开始下载</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🎯 文件管理页面加载');

            // 使用Flask-Login认证状态
            if (typeof window.ensureAuthSync === 'function') {
                console.log('🔄 检查认证状态');
                const isAuthenticated = await window.ensureAuthSync();
                if (isAuthenticated) {
                    console.log('✅ 已认证，加载文件');
                    loadFiles();
                } else {
                    console.log('❌ 未认证，显示登录提示');
                    showLoginPrompt();
                }
            } else {
                // 检查基本认证状态
                if (isAuthenticated && currentUser) {
                    loadFiles();
                } else {
                    showLoginPrompt();
                }
            }
        });

        // 旧的认证检查函数已删除，使用Flask-Login

        // 显示登录提示
        function showLoginPrompt() {
            // 隐藏加载状态和空状态
            hideLoading();
            document.getElementById('emptyState').style.display = 'none';

            // 在文件列表区域显示登录提示
            const filesList = document.getElementById('filesList');
            filesList.style.display = 'block';
            filesList.innerHTML = `
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-lock" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <h4>需要登录访问</h4>
                    <p class="text-muted mb-4">请先登录以查看和管理您的文件</p>
                    <button class="btn btn-primary" onclick="goToLogin()">
                        <i class="bi bi-box-arrow-in-right"></i> 立即登录
                    </button>
                </div>
            `;
        }

        // 跳转到登录页
        function goToLogin() {
            window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
        }

        // 跳转到登录页（兼容旧函数名）
        function redirectToLogin() {
            goToLogin();
        }

        // 加载文件列表 - 使用Flask-Login
        async function loadFiles() {
            try {
                showLoading();
                const response = await fetch('/api/files', {
                    credentials: 'same-origin'  // 使用cookies认证
                });
                const data = await response.json();
                
                if (data.success) {
                    displayFiles(data.files);
                    updateStats(data.total);
                } else {
                    showError('获取文件列表失败: ' + (data.error || '未知错误'));
                }
            } catch (error) {
                console.error('加载文件列表失败:', error);
                showError('网络错误，请检查连接');
            }
        }

        // 显示文件列表
        function displayFiles(files) {
            const filesList = document.getElementById('filesList');
            const emptyState = document.getElementById('emptyState');
            
            hideLoading();
            
            if (!files || files.length === 0) {
                filesList.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            emptyState.style.display = 'none';
            filesList.style.display = 'block';
            
            let html = '';
            files.forEach(file => {
                // 检查是否为视频文件
                const isVideo = isVideoFile(file.filename || '');

                html += `
                    <div class="file-item border rounded p-3 mb-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1">
                                    <i class="bi bi-${isVideo ? 'play-circle' : 'file-earmark-arrow-down'} text-primary"></i>
                                    ${escapeHtml(file.filename || '未知文件')}
                                    ${isVideo ? '<span class="badge bg-info ms-2">视频</span>' : ''}
                                </h6>
                                <small class="text-muted">
                                    <i class="bi bi-link-45deg"></i>
                                    ${escapeHtml(file.original_url || '')}
                                </small>
                            </div>
                            <div class="col-md-2 text-center">
                                <span class="file-size">${file.file_size_formatted || '未知'}</span>
                            </div>
                            <div class="col-md-2 text-center">
                                <span class="file-date">${file.created_at_formatted || '未知'}</span>
                            </div>
                            <div class="col-md-2 text-end">
                                ${isVideo ? `
                                    <button class="btn btn-primary btn-sm play-btn me-1"
                                            onclick="openVideoPlayer('${escapeHtml(file.filename)}', '${file.download_url}')">
                                        <i class="bi bi-play-fill"></i> 播放
                                    </button>
                                ` : ''}
                                <a href="${file.download_url}"
                                   class="btn btn-success btn-sm download-btn me-1"
                                   download="${escapeHtml(file.filename || 'download')}">
                                    <i class="bi bi-download"></i> 下载
                                </a>
                                <button class="btn btn-outline-danger btn-sm"
                                        onclick="deleteFile('${escapeHtml(file.filename)}', '${file.file_path || ''}')">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            filesList.innerHTML = html;
        }

        // 更新统计信息
        function updateStats(total) {
            document.getElementById('totalFiles').textContent = total || 0;
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('filesList').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        // 刷新文件列表
        function refreshFiles() {
            loadFiles();
        }

        // 删除单个文件
        async function deleteFile(filename, filePath) {
            if (!confirm(`确定要删除文件 "${filename}" 吗？\n\n此操作不可恢复！`)) {
                return;
            }

            try {
                const response = await fetch('/api/files/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'same-origin',  // 使用cookies认证
                    body: JSON.stringify({
                        filename: filename,
                        file_path: filePath
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(`文件 "${filename}" 已删除`);
                    // 刷新文件列表
                    loadFiles();
                } else {
                    showErrorMessage('删除失败: ' + (data.error || '未知错误'));
                }
            } catch (error) {
                console.error('删除文件失败:', error);
                showErrorMessage('删除失败: 网络错误');
            }
        }

        // 清理已完成的下载（占位功能）
        function clearCompleted() {
            if (confirm('确定要清理所有已完成的下载记录吗？')) {
                // TODO: 实现清理功能
                alert('清理功能待实现');
            }
        }

        // 显示错误信息
        function showError(message) {
            hideLoading();
            const filesList = document.getElementById('filesList');
            filesList.style.display = 'block';
            filesList.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    ${escapeHtml(message)}
                </div>
            `;
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            showMessage(message, 'success');
        }

        // 显示错误消息
        function showErrorMessage(message) {
            showMessage(message, 'danger');
        }

        // 显示消息
        function showMessage(message, type) {
            // 创建消息元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // 插入到页面顶部
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);

            // 5秒后自动隐藏
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 检查是否为视频文件
        function isVideoFile(filename) {
            const videoExtensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ogv'];
            const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
            return videoExtensions.includes(extension);
        }

        // 打开视频播放器（新窗口）
        function openVideoPlayer(filename, videoUrl) {
            try {
                console.log('🎬 打开视频播放器:', filename);
                console.log('📍 视频URL:', videoUrl);

                // 构建播放器页面URL
                const playerUrl = `/video-player?url=${encodeURIComponent(videoUrl)}&filename=${encodeURIComponent(filename)}`;

                // 在新窗口中打开播放器
                const playerWindow = window.open(
                    playerUrl,
                    'videoPlayer',
                    'width=1200,height=800,scrollbars=no,resizable=yes,status=no,toolbar=no,menubar=no,location=no'
                );

                if (playerWindow) {
                    playerWindow.focus();
                    console.log('✅ 视频播放器窗口已打开');
                } else {
                    // 如果弹窗被阻止，尝试在当前窗口打开
                    console.log('⚠️ 弹窗被阻止，在当前窗口打开');
                    window.location.href = playerUrl;
                }

            } catch (error) {
                console.error('❌ 打开视频播放器失败:', error);
                showErrorMessage('无法打开视频播放器: ' + error.message);
            }
        }
    </script>
{% endblock %}
