{% extends "base.html" %}

{% block title %}视频播放器{% endblock %}

{% block head %}
    <style>
        body {
            background: #000;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .video-player-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #000;
            display: flex;
            flex-direction: column;
        }

        .video-header {
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
        }

        .video-title {
            font-size: 1.2rem;
            margin: 0;
            flex: 1;
            color: white;
        }

        .video-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
            font-size: 14px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .video-content {
            flex: 1;
            position: relative;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        video {
            width: 100%;
            height: 100%;
            object-fit: contain;
            background: #000;
        }

        /* 简单的控制条样式 */
        video::-webkit-media-controls-panel {
            background-color: rgba(0, 0, 0, 0.8);
        }

        video::-webkit-media-controls-play-button,
        video::-webkit-media-controls-pause-button {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
        }

        video::-webkit-media-controls-timeline {
            background-color: rgba(255, 255, 255, 0.3);
        }

        video::-webkit-media-controls-current-time-display,
        video::-webkit-media-controls-time-remaining-display {
            color: white;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="video-player-container">
        <div class="video-header">
            <h1 class="video-title" id="videoTitle">视频播放器</h1>
            <div class="video-controls">
                <button class="control-btn" onclick="toggleFullscreen()">
                    <i class="bi bi-fullscreen"></i> 全屏
                </button>
                <button class="control-btn" onclick="downloadVideo()">
                    <i class="bi bi-download"></i> 下载
                </button>
                <button class="control-btn" onclick="closePlayer()">
                    <i class="bi bi-x-lg"></i> 关闭
                </button>
            </div>
        </div>
        
        <div class="video-content">
            <video
                id="videoPlayer"
                controls
                playsinline
                preload="metadata"
                style="width: 100%; height: 100%; object-fit: contain;">
                <p>您的浏览器不支持HTML5视频播放。</p>
            </video>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        let videoElement = null;
        let videoUrl = '';
        let filename = '';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL参数获取视频信息
            const urlParams = new URLSearchParams(window.location.search);
            videoUrl = urlParams.get('url');
            filename = urlParams.get('filename') || '未知视频';

            if (!videoUrl) {
                alert('缺少视频URL参数');
                window.close();
                return;
            }

            // 设置标题
            document.getElementById('videoTitle').textContent = decodeURIComponent(filename);
            document.title = `播放: ${decodeURIComponent(filename)}`;

            // 初始化播放器
            initializePlayer();
        });

        function initializePlayer() {
            try {
                console.log('🎮 初始化视频播放器...');
                console.log('📺 视频URL:', videoUrl);
                console.log('📁 文件名:', filename);

                // 设置视频源
                videoElement = document.getElementById('videoPlayer');
                videoElement.src = videoUrl;

                // 监听视频事件
                videoElement.addEventListener('loadeddata', () => {
                    console.log('✅ 视频已加载');
                });

                videoElement.addEventListener('error', (e) => {
                    console.error('❌ 视频加载错误:', e);
                    alert('视频播放失败，请检查文件格式或网络连接');
                });

                console.log('🎉 播放器初始化完成');

            } catch (error) {
                console.error('❌ 初始化播放器失败:', error);
                alert('播放器初始化失败: ' + error.message);
            }
        }

        // 切换全屏
        function toggleFullscreen() {
            if (videoElement) {
                if (videoElement.requestFullscreen) {
                    videoElement.requestFullscreen();
                } else if (videoElement.webkitRequestFullscreen) {
                    videoElement.webkitRequestFullscreen();
                } else if (videoElement.msRequestFullscreen) {
                    videoElement.msRequestFullscreen();
                }
            }
        }

        // 下载视频
        function downloadVideo() {
            const link = document.createElement('a');
            link.href = videoUrl;
            link.download = decodeURIComponent(filename);
            link.click();
        }

        // 关闭播放器
        function closePlayer() {
            window.close();
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'Escape':
                    closePlayer();
                    break;
                case ' ':
                    e.preventDefault();
                    if (videoElement) {
                        if (videoElement.paused) {
                            videoElement.play();
                        } else {
                            videoElement.pause();
                        }
                    }
                    break;
                case 'f':
                case 'F':
                    toggleFullscreen();
                    break;
            }
        });
    </script>
{% endblock %}
