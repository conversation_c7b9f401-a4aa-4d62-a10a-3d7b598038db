{% extends "base.html" %}

{% block title %}视频播放器{% endblock %}

{% block head %}
    <!-- Plyr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/plyr/3.7.8/plyr.css" />
    
    <style>
        body {
            background: #000;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .video-player-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #000;
            display: flex;
            flex-direction: column;
        }

        .video-header {
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
        }

        .video-title {
            font-size: 1.2rem;
            margin: 0;
            flex: 1;
        }

        .video-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .video-content {
            flex: 1;
            position: relative;
            background: #000;
        }

        .plyr {
            width: 100%;
            height: 100%;
        }

        .plyr video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* Plyr控件样式 */
        .plyr__controls {
            background: rgba(0, 0, 0, 0.8);
        }

        .plyr__control--overlaid {
            background: rgba(0, 179, 255, 0.9);
            border: 2px solid white;
        }

        .plyr__control--overlaid:hover {
            background: rgba(0, 179, 255, 1);
            transform: scale(1.05);
        }

        .plyr__progress__played {
            background: #00b3ff;
        }

        .plyr__volume {
            display: flex;
        }

        /* 确保控件可见 */
        .plyr__control {
            opacity: 1 !important;
            visibility: visible !important;
        }

        .plyr__controls {
            opacity: 1 !important;
            visibility: visible !important;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="video-player-container">
        <div class="video-header">
            <h1 class="video-title" id="videoTitle">视频播放器</h1>
            <div class="video-controls">
                <button class="control-btn" onclick="toggleFullscreen()">
                    <i class="bi bi-fullscreen"></i> 全屏
                </button>
                <button class="control-btn" onclick="downloadVideo()">
                    <i class="bi bi-download"></i> 下载
                </button>
                <button class="control-btn" onclick="closePlayer()">
                    <i class="bi bi-x-lg"></i> 关闭
                </button>
            </div>
        </div>
        
        <div class="video-content">
            <video
                id="videoPlayer"
                playsinline
                controls
                preload="metadata">
                <source src="" type="video/mp4" />
                <p>您的浏览器不支持HTML5视频播放。</p>
            </video>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <!-- Plyr JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/plyr/3.7.8/plyr.min.js"></script>

    <script>
        let player = null;
        let videoUrl = '';
        let filename = '';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL参数获取视频信息
            const urlParams = new URLSearchParams(window.location.search);
            videoUrl = urlParams.get('url');
            filename = urlParams.get('filename') || '未知视频';

            if (!videoUrl) {
                alert('缺少视频URL参数');
                window.close();
                return;
            }

            // 设置标题
            document.getElementById('videoTitle').textContent = decodeURIComponent(filename);
            document.title = `播放: ${decodeURIComponent(filename)}`;

            // 初始化播放器
            initializePlayer();
        });

        function initializePlayer() {
            try {
                console.log('🎮 初始化视频播放器...');
                console.log('📺 视频URL:', videoUrl);
                console.log('📁 文件名:', filename);

                // 设置视频源
                const videoElement = document.getElementById('videoPlayer');
                videoElement.src = videoUrl;

                // 创建Plyr播放器
                player = new Plyr('#videoPlayer', {
                    controls: [
                        'play-large',
                        'restart',
                        'rewind',
                        'play',
                        'fast-forward',
                        'progress',
                        'current-time',
                        'duration',
                        'mute',
                        'volume',
                        'settings',
                        'fullscreen'
                    ],
                    settings: ['speed'],
                    speed: { 
                        selected: 1, 
                        options: [0.5, 0.75, 1, 1.25, 1.5, 2] 
                    },
                    autoplay: false,
                    clickToPlay: true,
                    hideControls: false,
                    displayDuration: true,
                    fullscreen: { 
                        enabled: true, 
                        fallback: true, 
                        iosNative: true 
                    }
                });

                // 监听播放器事件
                player.on('ready', () => {
                    console.log('✅ 播放器已准备就绪');
                });

                player.on('error', (event) => {
                    console.error('❌ 播放器错误:', event);
                    alert('视频播放失败，请检查文件格式或网络连接');
                });

                console.log('🎉 播放器初始化完成');

            } catch (error) {
                console.error('❌ 初始化播放器失败:', error);
                alert('播放器初始化失败: ' + error.message);
            }
        }

        // 切换全屏
        function toggleFullscreen() {
            if (player) {
                player.fullscreen.toggle();
            }
        }

        // 下载视频
        function downloadVideo() {
            const link = document.createElement('a');
            link.href = videoUrl;
            link.download = decodeURIComponent(filename);
            link.click();
        }

        // 关闭播放器
        function closePlayer() {
            if (player) {
                player.destroy();
            }
            window.close();
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'Escape':
                    closePlayer();
                    break;
                case ' ':
                    e.preventDefault();
                    if (player) {
                        player.togglePlay();
                    }
                    break;
                case 'f':
                case 'F':
                    toggleFullscreen();
                    break;
            }
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (player) {
                player.destroy();
            }
        });
    </script>
{% endblock %}
