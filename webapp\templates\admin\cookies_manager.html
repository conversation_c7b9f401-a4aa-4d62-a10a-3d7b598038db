{% extends "base.html" %}

{% block title %}Cookies管理 - 管理员{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>🍪 YouTube Cookies管理</h2>
                <div>
                    <button id="refreshStatus" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt"></i> 刷新状态
                    </button>
                    <a href="/admin" class="btn btn-secondary ms-2">
                        <i class="fas fa-arrow-left"></i> 返回管理
                    </a>
                </div>
            </div>

            <!-- 状态卡片 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title">Cookies状态</h5>
                            <div id="cookiesStatusBadge" class="mb-2">
                                <span class="badge bg-secondary">检查中...</span>
                            </div>
                            <p id="cookiesStatusText" class="card-text text-muted">正在检查状态...</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h5 class="card-title">有效性测试</h5>
                            <div id="validityBadge" class="mb-2">
                                <span class="badge bg-secondary">未测试</span>
                            </div>
                            <button id="testCookies" class="btn btn-sm btn-info">
                                <i class="fas fa-vial"></i> 测试
                            </button>
                            <button id="debugCookies" class="btn btn-sm btn-outline-secondary ms-1" title="显示详细调试信息">
                                <i class="fas fa-bug"></i> 调试
                            </button>
                            <button id="debugContent" class="btn btn-sm btn-outline-warning ms-1" title="检查当前cookies文件内容">
                                <i class="fas fa-file-alt"></i> 内容
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title">备份数量</h5>
                            <div id="backupCount" class="mb-2">
                                <span class="badge bg-secondary">0</span>
                            </div>
                            <button id="refreshBackups" class="btn btn-sm btn-success">
                                <i class="fas fa-list"></i> 查看
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要功能标签页 -->
            <ul class="nav nav-tabs" id="cookiesTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button">
                        <i class="fas fa-upload"></i> 导入Cookies
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button">
                        <i class="fas fa-archive"></i> 备份管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="help-tab" data-bs-toggle="tab" data-bs-target="#help" type="button">
                        <i class="fas fa-question-circle"></i> 获取指南
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="cookiesTabContent">
                <!-- 导入Cookies标签页 -->
                <div class="tab-pane fade show active" id="import" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-upload"></i> 导入新的Cookies</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 支持的格式:</h6>
                                <ul class="mb-0">
                                    <li><strong>JSON格式:</strong> 浏览器扩展导出的JSON文件内容</li>
                                    <li><strong>Netscape格式:</strong> 标准的cookies.txt格式</li>
                                    <li><strong>自动检测:</strong> 系统会自动识别格式并转换</li>
                                </ul>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="cookiesFormat" class="form-label">Cookies格式</label>
                                    <select class="form-select" id="cookiesFormat">
                                        <option value="auto">自动检测</option>
                                        <option value="json">JSON格式</option>
                                        <option value="netscape">Netscape格式</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">快速操作</label>
                                    <div>
                                        <button id="clearContent" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-trash"></i> 清空
                                        </button>
                                        <button id="pasteExample" class="btn btn-outline-info btn-sm ms-2">
                                            <i class="fas fa-paste"></i> 示例
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="cookiesContent" class="form-label">Cookies内容</label>
                                <textarea class="form-control font-monospace" id="cookiesContent" rows="12" 
                                          placeholder="粘贴您的cookies内容...

支持格式：
1. JSON格式 (浏览器扩展导出)
2. Netscape格式 (cookies.txt)

示例 JSON 格式：
[{&quot;domain&quot;: &quot;.youtube.com&quot;, &quot;name&quot;: &quot;SID&quot;, &quot;value&quot;: &quot;...&quot;}]

示例 Netscape 格式：
# Netscape HTTP Cookie File
.youtube.com	TRUE	/	FALSE	1234567890	SID	value"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <button id="validateCookies" class="btn btn-outline-primary">
                                        <i class="fas fa-check"></i> 验证格式
                                    </button>
                                    <button id="importCookies" class="btn btn-success ms-2">
                                        <i class="fas fa-upload"></i> 导入Cookies
                                    </button>
                                </div>
                                <div class="col-md-6 text-end">
                                    <small class="text-muted">导入前会自动备份现有cookies</small>
                                </div>
                            </div>

                            <div id="importResult" class="mt-3"></div>
                        </div>
                    </div>
                </div>

                <!-- 备份管理标签页 -->
                <div class="tab-pane fade" id="backup" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-archive"></i> Cookies备份管理</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <button id="refreshBackupsList" class="btn btn-primary">
                                        <i class="fas fa-sync-alt"></i> 刷新列表
                                    </button>
                                    <button id="cleanupOldBackups" class="btn btn-warning ms-2">
                                        <i class="fas fa-broom"></i> 清理旧备份
                                    </button>
                                </div>
                                <small class="text-muted">备份文件保存在: /app/config/</small>
                            </div>

                            <div id="backupsList">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> 加载备份列表...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 获取指南标签页 -->
                <div class="tab-pane fade" id="help" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-question-circle"></i> Cookies获取指南</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-chrome"></i> Chrome浏览器</h6>
                                    <ol>
                                        <li>安装扩展: "Get cookies.txt LOCALLY"</li>
                                        <li>访问并登录 <a href="https://youtube.com" target="_blank">YouTube</a></li>
                                        <li>点击扩展图标</li>
                                        <li>选择 "youtube.com"</li>
                                        <li>复制JSON内容到导入框</li>
                                    </ol>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-firefox-browser"></i> Firefox浏览器</h6>
                                    <ol>
                                        <li>安装扩展: "cookies.txt"</li>
                                        <li>访问并登录 <a href="https://youtube.com" target="_blank">YouTube</a></li>
                                        <li>右键点击页面 → "Export cookies"</li>
                                        <li>保存为 cookies.txt</li>
                                        <li>复制内容到导入框</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="alert alert-warning mt-3">
                                <h6><i class="fas fa-exclamation-triangle"></i> 重要提醒</h6>
                                <ul class="mb-0">
                                    <li>导入前会自动备份现有cookies</li>
                                    <li>支持格式自动检测和转换</li>
                                    <li>导入后会自动测试有效性</li>
                                    <li>建议每3个月更新一次</li>
                                    <li>保持YouTube账号活跃状态</li>
                                </ul>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-shield-alt"></i> 安全说明</h6>
                                <ul class="mb-0">
                                    <li>Cookies仅用于YouTube视频下载</li>
                                    <li>不会存储您的密码信息</li>
                                    <li>您可以随时更新或删除cookies</li>
                                    <li>建议使用专门的YouTube账号</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 页面加载时检查状态
    checkCookiesStatus();
    loadBackupsList();

    // 刷新状态按钮
    document.getElementById('refreshStatus').addEventListener('click', function() {
        checkCookiesStatus();
        loadBackupsList();
    });

    // 测试cookies按钮
    document.getElementById('testCookies').addEventListener('click', testCookiesValidity);

    // 调试cookies按钮
    document.getElementById('debugCookies').addEventListener('click', debugCookiesInfo);

    // 调试内容按钮
    document.getElementById('debugContent').addEventListener('click', debugCookiesContent);

    // 导入相关按钮
    document.getElementById('validateCookies').addEventListener('click', validateCookiesFormat);
    document.getElementById('importCookies').addEventListener('click', importCookies);
    document.getElementById('clearContent').addEventListener('click', () => {
        document.getElementById('cookiesContent').value = '';
    });
    document.getElementById('pasteExample').addEventListener('click', pasteExample);

    // 备份相关按钮
    document.getElementById('refreshBackups').addEventListener('click', loadBackupsList);
    document.getElementById('refreshBackupsList').addEventListener('click', loadBackupsList);
    document.getElementById('cleanupOldBackups').addEventListener('click', cleanupOldBackups);

    // 检查cookies状态
    function checkCookiesStatus() {
        fetch('/api/cookies/status', {
            credentials: 'same-origin'  // 使用cookies认证
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatusDisplay(data);
            } else {
                showError('检查状态失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('检查状态失败: ' + error.message);
        });
    }

    // 更新状态显示
    function updateStatusDisplay(status) {
        const statusBadge = document.getElementById('cookiesStatusBadge');
        const statusText = document.getElementById('cookiesStatusText');

        let badgeClass = 'bg-secondary';
        let statusMessage = '未知状态';

        if (status.exists) {
            switch (status.status) {
                case 'good':
                    badgeClass = 'bg-success';
                    statusMessage = '状态良好';
                    break;
                case 'warning':
                    badgeClass = 'bg-warning';
                    statusMessage = '需要关注';
                    break;
                case 'expired':
                    badgeClass = 'bg-danger';
                    statusMessage = '可能过期';
                    break;
                case 'incomplete':
                    badgeClass = 'bg-warning';
                    statusMessage = '内容不完整';
                    break;
            }
        } else {
            badgeClass = 'bg-danger';
            statusMessage = '不存在';
        }

        statusBadge.innerHTML = `<span class="badge ${badgeClass}">${statusMessage}</span>`;
        statusText.textContent = status.message || '无详细信息';
    }

    // 测试cookies有效性
    function testCookiesValidity() {
        const btn = document.getElementById('testCookies');
        const badge = document.getElementById('validityBadge');

        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中';
        badge.innerHTML = '<span class="badge bg-secondary">测试中...</span>';

        fetch('/api/cookies/test', {
            credentials: 'same-origin'  // 使用cookies认证
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.valid) {
                    badge.innerHTML = '<span class="badge bg-success">有效</span>';
                    showSuccess('Cookies测试通过: ' + (data.message || '可以正常下载YouTube视频'));
                } else {
                    badge.innerHTML = '<span class="badge bg-danger">无效</span>';
                    showError('Cookies测试失败: ' + (data.message || data.error));
                }
            } else {
                badge.innerHTML = '<span class="badge bg-danger">错误</span>';
                showError('测试失败: ' + data.error);
            }
        })
        .catch(error => {
            badge.innerHTML = '<span class="badge bg-danger">错误</span>';
            showError('测试失败: ' + error.message);
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-vial"></i> 测试';
        });
    }

    // 调试cookies信息
    function debugCookiesInfo() {
        const btn = document.getElementById('debugCookies');

        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 调试中';

        // 获取详细状态信息
        Promise.all([
            fetch('/api/cookies/status', { credentials: 'same-origin' }).then(r => r.json()),
            fetch('/api/cookies/test', { credentials: 'same-origin' }).then(r => r.json()),
            fetch('/api/cookies/backup/list', { credentials: 'same-origin' }).then(r => r.json())
        ])
        .then(([statusData, testData, backupData]) => {
            let debugInfo = '🔍 **Cookies调试信息**\n\n';

            // 状态信息
            debugInfo += '📊 **状态信息:**\n';
            debugInfo += `- 文件存在: ${statusData.exists ? '✅ 是' : '❌ 否'}\n`;
            if (statusData.exists) {
                debugInfo += `- 状态: ${statusData.status}\n`;
                debugInfo += `- 消息: ${statusData.message}\n`;
                debugInfo += `- 重要cookies: ${statusData.important_cookies ? statusData.important_cookies.join(', ') : '无'}\n`;
            }

            debugInfo += '\n🧪 **测试结果:**\n';
            debugInfo += `- 测试成功: ${testData.success ? '✅ 是' : '❌ 否'}\n`;
            debugInfo += `- Cookies有效: ${testData.valid ? '✅ 是' : '❌ 否'}\n`;
            debugInfo += `- 测试消息: ${testData.message || '无'}\n`;
            if (testData.error) {
                debugInfo += `- 错误信息: ${testData.error}\n`;
            }

            debugInfo += '\n💾 **备份信息:**\n';
            debugInfo += `- 备份数量: ${backupData.success ? backupData.backups.length : '获取失败'}\n`;
            if (backupData.success && backupData.backups.length > 0) {
                debugInfo += '- 最新备份:\n';
                backupData.backups.slice(0, 3).forEach(backup => {
                    debugInfo += `  • ${backup.filename} (${backup.created_time_str})\n`;
                });
            }

            debugInfo += '\n💡 **建议操作:**\n';
            if (!statusData.exists) {
                debugInfo += '- 请先导入有效的cookies文件\n';
            } else if (!testData.valid) {
                debugInfo += '- 当前cookies可能已过期，建议重新获取\n';
                debugInfo += '- 可以尝试恢复最近的备份\n';
            } else {
                debugInfo += '- Cookies状态正常，可以正常使用\n';
            }

            // 显示调试信息
            showDebugModal(debugInfo);
        })
        .catch(error => {
            showError('获取调试信息失败: ' + error.message);
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-bug"></i> 调试';
        });
    }

    // 显示调试信息模态框
    function showDebugModal(debugInfo) {
        // 创建模态框
        const modalHtml = `
            <div class="modal fade" id="debugModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">🔍 Cookies调试信息</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">${debugInfo}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" onclick="copyDebugInfo()">复制信息</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('debugModal');
        if (oldModal) {
            oldModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('debugModal'));
        modal.show();

        // 存储调试信息供复制使用
        window.currentDebugInfo = debugInfo;
    }

    // 复制调试信息
    window.copyDebugInfo = function() {
        if (window.currentDebugInfo) {
            navigator.clipboard.writeText(window.currentDebugInfo).then(() => {
                showSuccess('调试信息已复制到剪贴板');
            }).catch(() => {
                showWarning('复制失败，请手动选择文本复制');
            });
        }
    };

    // 调试cookies文件内容
    function debugCookiesContent() {
        const btn = document.getElementById('debugContent');

        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中';

        fetch('/api/cookies/debug/content', {
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showContentDebugModal(data);
            } else {
                showError('获取内容调试信息失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('获取内容调试信息失败: ' + error.message);
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-file-alt"></i> 内容';
        });
    }

    // 显示内容调试信息模态框
    function showContentDebugModal(data) {
        let debugInfo = '📄 **Cookies文件内容分析**\n\n';

        debugInfo += '📁 **文件基本信息:**\n';
        debugInfo += `- 文件路径: ${data.file_info.path}\n`;
        debugInfo += `- 文件大小: ${(data.file_info.size_bytes / 1024).toFixed(1)} KB (${data.file_info.size_chars} 字符)\n`;
        debugInfo += `- 总行数: ${data.file_info.total_lines}\n`;
        debugInfo += `- 有效行数: ${data.file_info.valid_lines}\n`;
        debugInfo += `- 注释行数: ${data.file_info.comment_lines}\n`;
        debugInfo += `- 空行数: ${data.file_info.empty_lines}\n`;

        debugInfo += '\n📊 **内容分析:**\n';
        debugInfo += `- YouTube相关行: ${data.content_analysis.youtube_lines}\n`;
        debugInfo += `- Google相关行: ${data.content_analysis.google_lines}\n`;
        debugInfo += `- 其他域名行: ${data.content_analysis.other_lines}\n`;
        debugInfo += `- 总域名数: ${data.content_analysis.total_domains}\n`;

        debugInfo += '\n🔑 **重要Cookies详细分析:**\n';
        debugInfo += `- 找到数量: ${data.important_cookies.found_count}/7\n`;
        debugInfo += `- 找到列表: [${data.important_cookies.found_list.join(', ') || '无'}]\n\n`;

        // 详细的重要cookies状态
        const cookieNames = ['SID', 'HSID', 'SSID', 'APISID', 'SAPISID', 'LOGIN_INFO', 'VISITOR_INFO1_LIVE'];
        cookieNames.forEach(cookieName => {
            const detail = data.important_cookies.details[cookieName];
            if (detail.found) {
                debugInfo += `✅ ${cookieName}: 找到 (域名: ${detail.domain}, 行号: ${detail.line_number})\n`;
                debugInfo += `   值预览: ${detail.value_preview}\n`;
            } else {
                debugInfo += `❌ ${cookieName}: 未找到\n`;
            }
        });

        if (data.format_issues && data.format_issues.length > 0) {
            debugInfo += '\n⚠️ **格式问题:**\n';
            data.format_issues.forEach(issue => {
                debugInfo += `- 行 ${issue.line_number}: 字段数量 ${issue.parts_count} (期望≥7)\n`;
                debugInfo += `  内容: ${issue.content}\n`;
            });
        } else {
            debugInfo += '\n✅ **格式检查:** 无明显格式问题\n';
        }

        debugInfo += '\n📋 **前10行内容分析:**\n';
        data.line_by_line.forEach(line => {
            const status = line.is_valid_format ? '✅' : '❌';
            debugInfo += `${status} 行${line.line_number}: ${line.parts_count}字段, 域名: ${line.domain}, Cookie: ${line.cookie_name}\n`;
        });

        debugInfo += '\n📄 **文件内容预览 (前500字符):**\n';
        debugInfo += '```\n';
        debugInfo += data.sample_content;
        debugInfo += '\n```\n';

        debugInfo += '\n💡 **问题诊断:**\n';
        if (data.important_cookies.found_count === 0) {
            debugInfo += '🚨 **严重问题**: 没有找到任何重要的YouTube认证cookies\n';
            debugInfo += '📋 **可能原因**:\n';
            debugInfo += '  • cookies文件格式不正确\n';
            debugInfo += '  • 导入的cookies不是来自已登录的YouTube账号\n';
            debugInfo += '  • cookies文件已损坏或不完整\n';
            debugInfo += '  • 域名不匹配 (应该包含 .youtube.com 或 .google.com)\n';
            debugInfo += '\n🔧 **建议解决方案**:\n';
            debugInfo += '  1. 重新从浏览器导出cookies (确保已登录YouTube)\n';
            debugInfo += '  2. 检查导出格式是否正确 (Netscape格式)\n';
            debugInfo += '  3. 确保包含 .youtube.com 和 .google.com 域名的cookies\n';
        } else if (data.important_cookies.found_count < 3) {
            debugInfo += '⚠️ **部分问题**: 重要cookies不完整\n';
            debugInfo += '📋 **缺少的关键cookies可能导致认证失败**\n';
            debugInfo += '🔧 **建议**: 重新获取完整的cookies\n';
        } else {
            debugInfo += '✅ **状态良好**: 找到了足够的重要cookies\n';
            debugInfo += '🔧 **建议**: 如果仍然无法使用，可能是cookies已过期\n';
        }

        // 创建内容调试模态框
        const modalHtml = `
            <div class="modal fade" id="contentDebugModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">📄 Cookies文件内容分析</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 500px; overflow-y: auto;">${debugInfo}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" onclick="copyContentDebugInfo()">复制分析</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('contentDebugModal');
        if (oldModal) {
            oldModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('contentDebugModal'));
        modal.show();

        // 存储内容调试信息供复制使用
        window.currentContentDebugInfo = debugInfo;
    }

    // 复制内容调试信息
    window.copyContentDebugInfo = function() {
        if (window.currentContentDebugInfo) {
            navigator.clipboard.writeText(window.currentContentDebugInfo).then(() => {
                showSuccess('内容分析已复制到剪贴板');
            }).catch(() => {
                showWarning('复制失败，请手动选择文本复制');
            });
        }
    };

    // 验证cookies格式
    function validateCookiesFormat() {
        const content = document.getElementById('cookiesContent').value.trim();
        if (!content) {
            showError('请先输入cookies内容');
            return;
        }

        try {
            // 尝试解析JSON
            JSON.parse(content);
            showSuccess('检测到有效的JSON格式');
        } catch (e) {
            // 检查Netscape格式
            if (content.includes('# Netscape HTTP Cookie File') || 
                (content.includes('\t') && content.includes('youtube.com'))) {
                showSuccess('检测到Netscape格式');
            } else {
                showWarning('格式可能不正确，请检查内容');
            }
        }
    }

    // 导入cookies
    function importCookies() {
        const content = document.getElementById('cookiesContent').value.trim();
        const format = document.getElementById('cookiesFormat').value;

        if (!content) {
            showError('请先输入cookies内容');
            return;
        }

        const btn = document.getElementById('importCookies');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导入中';

        fetch('/api/cookies/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',  // 使用cookies认证
            body: JSON.stringify({
                cookies_content: content,
                format: format
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('Cookies导入成功！找到 ' + data.found_cookies.length + ' 个重要cookies');
                document.getElementById('cookiesContent').value = '';
                checkCookiesStatus(); // 刷新状态
                
                // 显示测试结果
                if (data.test_result) {
                    if (data.test_result.valid) {
                        showSuccess('导入的cookies测试通过，可以正常使用');
                    } else {
                        showWarning('导入成功但测试失败，可能需要重新获取cookies');
                    }
                }
            } else {
                showError('导入失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('导入失败: ' + error.message);
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-upload"></i> 导入Cookies';
        });
    }

    // 粘贴示例
    function pasteExample() {
        const example = `[
    {
        "domain": ".youtube.com",
        "name": "SID",
        "value": "your_sid_value_here",
        "path": "/",
        "secure": false,
        "expirationDate": 1767225600
    },
    {
        "domain": ".youtube.com", 
        "name": "HSID",
        "value": "your_hsid_value_here",
        "path": "/",
        "secure": false,
        "expirationDate": 1767225600
    }
]`;
        document.getElementById('cookiesContent').value = example;
        showInfo('已粘贴示例格式，请替换为您的实际cookies值');
    }

    // 加载备份列表
    function loadBackupsList() {
        fetch('/api/cookies/backup/list', {
            credentials: 'same-origin'  // 使用cookies认证
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayBackupsList(data.backups);
                document.getElementById('backupCount').innerHTML = 
                    `<span class="badge bg-success">${data.backups.length}</span>`;
            } else {
                showError('加载备份列表失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('加载备份列表失败: ' + error.message);
        });
    }

    // 显示备份列表
    function displayBackupsList(backups) {
        const container = document.getElementById('backupsList');
        
        if (backups.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">暂无备份文件</div>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr><th>文件名</th><th>创建时间</th><th>大小</th><th>操作</th></tr></thead><tbody>';

        backups.forEach(backup => {
            const sizeKB = (backup.file_size / 1024).toFixed(1);
            html += `
                <tr>
                    <td><code>${backup.filename}</code></td>
                    <td>${backup.created_time_str}</td>
                    <td>${sizeKB} KB</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-info" onclick="inspectBackup('${backup.filename}')" title="检查备份内容">
                                <i class="fas fa-search"></i> 检查
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="restoreBackup('${backup.filename}', false)" title="直接恢复（不创建备份）">
                                <i class="fas fa-undo"></i> 恢复
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="restoreBackup('${backup.filename}', true)" title="恢复并备份当前cookies">
                                <i class="fas fa-shield-alt"></i> 安全恢复
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup('${backup.filename}')">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    // 检查备份文件
    window.inspectBackup = function(filename) {
        fetch('/api/cookies/backup/inspect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                backup_filename: filename
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showBackupInspectionModal(data);
            } else {
                showError('检查备份失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('检查备份失败: ' + error.message);
        });
    };

    // 显示备份检查结果
    function showBackupInspectionModal(data) {
        const isComplete = data.is_complete;
        const completenessIcon = isComplete ? '✅' : '⚠️';
        const completenessText = isComplete ? '完整' : '不完整';

        let inspectionInfo = `🔍 **备份文件检查报告**\n\n`;
        inspectionInfo += `📁 **文件信息:**\n`;
        inspectionInfo += `- 文件名: ${data.filename}\n`;
        inspectionInfo += `- 文件大小: ${(data.file_size / 1024).toFixed(1)} KB\n`;
        inspectionInfo += `- 修改时间: ${data.modified_time}\n`;
        inspectionInfo += `- 有效行数: ${data.valid_lines}\n`;

        inspectionInfo += `\n🔑 **重要Cookies分析:**\n`;
        inspectionInfo += `- 完整性: ${completenessIcon} ${completenessText}\n`;
        inspectionInfo += `- 重要cookies数量: ${data.important_cookies_count}/7\n`;
        inspectionInfo += `- 找到的cookies: ${data.important_cookies.join(', ') || '无'}\n`;

        inspectionInfo += `\n📊 **详细分析:**\n`;
        inspectionInfo += `- SID (会话ID): ${data.analysis.has_sid ? '✅' : '❌'}\n`;
        inspectionInfo += `- HSID (HTTP会话ID): ${data.analysis.has_hsid ? '✅' : '❌'}\n`;
        inspectionInfo += `- SSID (安全会话ID): ${data.analysis.has_ssid ? '✅' : '❌'}\n`;
        inspectionInfo += `- APISID (API会话ID): ${data.analysis.has_apisid ? '✅' : '❌'}\n`;
        inspectionInfo += `- SAPISID (安全API会话ID): ${data.analysis.has_sapisid ? '✅' : '❌'}\n`;
        inspectionInfo += `- LOGIN_INFO (登录信息): ${data.analysis.has_login_info ? '✅' : '❌'}\n`;
        inspectionInfo += `- VISITOR_INFO1_LIVE (访客信息): ${data.analysis.has_visitor_info ? '✅' : '❌'}\n`;

        inspectionInfo += `\n💡 **建议:**\n`;
        if (isComplete) {
            inspectionInfo += `- ✅ 此备份包含完整的认证cookies，可以安全恢复\n`;
            inspectionInfo += `- 🎯 建议使用此备份恢复cookies功能\n`;
        } else {
            inspectionInfo += `- ⚠️ 此备份缺少重要的认证cookies\n`;
            inspectionInfo += `- 🚫 不建议恢复此备份，可能无法正常使用\n`;
            inspectionInfo += `- 💡 建议重新获取完整的cookies\n`;
        }

        // 创建检查结果模态框
        const modalHtml = `
            <div class="modal fade" id="inspectionModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">🔍 备份文件检查报告</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">${inspectionInfo}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            ${isComplete ?
                                `<button type="button" class="btn btn-success" onclick="restoreBackupFromModal('${data.filename}')">恢复此备份</button>` :
                                `<button type="button" class="btn btn-outline-secondary" disabled>不建议恢复</button>`
                            }
                            <button type="button" class="btn btn-primary" onclick="copyInspectionInfo()">复制报告</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('inspectionModal');
        if (oldModal) {
            oldModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('inspectionModal'));
        modal.show();

        // 存储检查信息供复制使用
        window.currentInspectionInfo = inspectionInfo;
    }

    // 从模态框恢复备份
    window.restoreBackupFromModal = function(filename) {
        // 关闭检查模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('inspectionModal'));
        if (modal) {
            modal.hide();
        }

        // 执行恢复
        setTimeout(() => {
            restoreBackup(filename, true); // 安全恢复
        }, 300);
    };

    // 复制检查信息
    window.copyInspectionInfo = function() {
        if (window.currentInspectionInfo) {
            navigator.clipboard.writeText(window.currentInspectionInfo).then(() => {
                showSuccess('检查报告已复制到剪贴板');
            }).catch(() => {
                showWarning('复制失败，请手动选择文本复制');
            });
        }
    };

    // 显示恢复对比报告
    function showRestoreComparisonModal(data) {
        const comparison = data.comparison;
        const beforeCookies = data.before_cookies || [];
        const afterCookies = data.after_cookies || [];
        const backupCookies = data.backup_cookies || [];

        let comparisonInfo = `🔄 **Cookies恢复对比报告**\n\n`;

        comparisonInfo += `📊 **恢复概况:**\n`;
        comparisonInfo += `- 恢复文件: ${data.restored_from}\n`;
        comparisonInfo += `- 恢复状态: ${comparison.restoration_success ? '✅ 完全成功' : '⚠️ 部分成功'}\n`;
        comparisonInfo += `- 整体改善: ${comparison.improvement ? '✅ 是' : '❌ 否'}\n`;

        comparisonInfo += `\n📈 **数量对比:**\n`;
        comparisonInfo += `- 恢复前: ${beforeCookies.length} 个重要cookies\n`;
        comparisonInfo += `- 备份中: ${backupCookies.length} 个重要cookies\n`;
        comparisonInfo += `- 恢复后: ${afterCookies.length} 个重要cookies\n`;

        comparisonInfo += `\n🔍 **详细变化:**\n`;

        if (comparison.added.length > 0) {
            comparisonInfo += `✅ **新增cookies (${comparison.added.length}个):**\n`;
            comparison.added.forEach(cookie => {
                comparisonInfo += `  • ${cookie}\n`;
            });
        } else {
            comparisonInfo += `❌ **新增cookies:** 无\n`;
        }

        if (comparison.lost.length > 0) {
            comparisonInfo += `\n❌ **丢失cookies (${comparison.lost.length}个):**\n`;
            comparison.lost.forEach(cookie => {
                comparisonInfo += `  • ${cookie}\n`;
            });
        } else {
            comparisonInfo += `\n✅ **丢失cookies:** 无\n`;
        }

        if (comparison.kept.length > 0) {
            comparisonInfo += `\n🔄 **保持cookies (${comparison.kept.length}个):**\n`;
            comparison.kept.forEach(cookie => {
                comparisonInfo += `  • ${cookie}\n`;
            });
        }

        comparisonInfo += `\n📋 **完整列表对比:**\n`;
        comparisonInfo += `- 恢复前: [${beforeCookies.join(', ') || '无'}]\n`;
        comparisonInfo += `- 备份中: [${backupCookies.join(', ') || '无'}]\n`;
        comparisonInfo += `- 恢复后: [${afterCookies.join(', ') || '无'}]\n`;

        comparisonInfo += `\n💡 **结果分析:**\n`;
        if (comparison.restoration_success) {
            comparisonInfo += `- ✅ 恢复完全成功，备份中的所有cookies都已正确恢复\n`;
        } else {
            comparisonInfo += `- ⚠️ 恢复不完全，可能存在以下问题:\n`;
            comparisonInfo += `  • 备份文件可能已损坏\n`;
            comparisonInfo += `  • 文件权限问题\n`;
            comparisonInfo += `  • 格式转换问题\n`;
        }

        if (comparison.improvement) {
            comparisonInfo += `- 🎯 恢复后cookies数量增加，功能应该有所改善\n`;
        } else {
            comparisonInfo += `- 📉 恢复后cookies数量未增加，可能需要重新获取新的cookies\n`;
        }

        // 创建对比报告模态框
        const modalHtml = `
            <div class="modal fade" id="comparisonModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">🔄 Cookies恢复对比报告</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">${comparisonInfo}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-info" onclick="testCookiesAfterRestore()">测试恢复效果</button>
                            <button type="button" class="btn btn-primary" onclick="copyComparisonInfo()">复制报告</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('comparisonModal');
        if (oldModal) {
            oldModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('comparisonModal'));
        modal.show();

        // 存储对比信息供复制使用
        window.currentComparisonInfo = comparisonInfo;
    }

    // 恢复后测试cookies
    window.testCookiesAfterRestore = function() {
        // 关闭对比模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('comparisonModal'));
        if (modal) {
            modal.hide();
        }

        // 执行测试
        setTimeout(() => {
            testCookiesValidity();
        }, 300);
    };

    // 复制对比信息
    window.copyComparisonInfo = function() {
        if (window.currentComparisonInfo) {
            navigator.clipboard.writeText(window.currentComparisonInfo).then(() => {
                showSuccess('对比报告已复制到剪贴板');
            }).catch(() => {
                showWarning('复制失败，请手动选择文本复制');
            });
        }
    };

    // 删除备份文件
    window.deleteBackup = function(filename) {
        if (!confirm(`确定要删除备份文件 "${filename}" 吗？\n\n此操作不可恢复！`)) {
            return;
        }

        fetch('/api/cookies/backup/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',  // 使用cookies认证
            body: JSON.stringify({
                backup_filename: filename
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('备份文件已删除: ' + filename);
                loadBackupsList(); // 刷新备份列表
            } else {
                showError('删除失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('删除失败: ' + error.message);
        });
    };

    // 恢复备份
    window.restoreBackup = function(filename, createBackup = false) {
        let confirmMessage;
        if (createBackup) {
            confirmMessage = `确定要安全恢复备份 "${filename}" 吗？\n\n这将：\n1. 先备份当前cookies\n2. 然后恢复选择的备份`;
        } else {
            confirmMessage = `确定要直接恢复备份 "${filename}" 吗？\n\n⚠️ 这将直接替换当前cookies，不会创建备份！`;
        }

        if (!confirm(confirmMessage)) {
            return;
        }

        fetch('/api/cookies/backup/restore', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                backup_filename: filename,
                create_backup: createBackup
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 显示基本成功消息
                showSuccess('✅ ' + data.message);

                // 如果有对比数据，显示详细的恢复报告
                if (data.comparison) {
                    setTimeout(() => {
                        showRestoreComparisonModal(data);
                    }, 1000);
                }

                checkCookiesStatus(); // 刷新状态
                loadBackupsList(); // 刷新备份列表
            } else {
                showError('❌ 恢复失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('❌ 恢复失败: ' + error.message);
        });
    };

    // 清理旧备份
    function cleanupOldBackups() {
        if (!confirm('确定要清理旧备份吗？\n\n这将删除除最新3个备份外的所有备份文件！')) {
            return;
        }

        fetch('/api/cookies/backup/list', {
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.backups.length > 3) {
                // 按时间排序，保留最新的3个
                const sortedBackups = data.backups.sort((a, b) => b.created_time - a.created_time);
                const toDelete = sortedBackups.slice(3); // 删除第4个及以后的

                if (toDelete.length === 0) {
                    showInfo('没有需要清理的旧备份');
                    return;
                }

                // 批量删除
                let deleteCount = 0;
                let totalCount = toDelete.length;

                showInfo(`开始清理 ${totalCount} 个旧备份...`);

                toDelete.forEach((backup, index) => {
                    fetch('/api/cookies/backup/delete', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        credentials: 'same-origin',
                        body: JSON.stringify({ backup_filename: backup.filename })
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            deleteCount++;
                        }

                        // 如果是最后一个
                        if (index === totalCount - 1) {
                            showSuccess(`✅ 清理完成！删除了 ${deleteCount}/${totalCount} 个旧备份`);
                            loadBackupsList(); // 刷新列表
                        }
                    })
                    .catch(error => {
                        console.error('删除备份失败:', error);
                        if (index === totalCount - 1) {
                            showWarning(`⚠️ 清理完成，但有 ${totalCount - deleteCount} 个文件删除失败`);
                            loadBackupsList();
                        }
                    });
                });
            } else {
                showInfo('备份文件不超过3个，无需清理');
            }
        })
        .catch(error => {
            showError('获取备份列表失败: ' + error.message);
        });
    }

    // 消息显示函数
    function showSuccess(message) {
        showMessage(message, 'success');
    }

    function showError(message) {
        showMessage(message, 'danger');
    }

    function showWarning(message) {
        showMessage(message, 'warning');
    }

    function showInfo(message) {
        showMessage(message, 'info');
    }

    function showMessage(message, type) {
        const result = document.getElementById('importResult');
        result.innerHTML = `<div class="alert alert-${type} alert-dismissible fade show">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    }
});
</script>
{% endblock %}
